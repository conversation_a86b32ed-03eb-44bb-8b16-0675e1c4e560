auth:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
logger:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
pureengage:
  host: http://*************:8090
  recordsLimit: 200
genesys-cloud:
  environment: mypurecloud.com.au
  apiHost: https://api.mypurecloud.com.au
  authHost: https://login.mypurecloud.com.au
  embeddingGC:
    oAuthClientId: 15d67172-1791-4668-9f42-e4e7e88f1880
    redirectUri: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com/ctint/mf-cdss/login?embedding-gc=true
loginTotal: 1
missingTotal: 3
poolTotal: 10000
heartbeatTime: 30
socketType: CDSS
recording:
  mediaSource:
  - aws
ctint-dab:
  auth:
    host: http://ctint-dab-auth-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  config:
    host: http://ctint-dab-config-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  conv:
    host: http://ctint-dab-conv-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true
  user:
    host: http://ctint-dab-user-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  dataSync:
    host: http://ctint-dab-datasync-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true  
  callControl:
    host: http://ctint-dab-call-control-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true
  jobWorker:
    host: http://ctint-dab-job-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true
  common:
    host: http://ctint-graphql-service.cdss-data-ctint.svc.cluster.local:8110 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /ctint/query
    active: true
  auditLog:
    host: http://ctint-dab-audit-log-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  qm:
    host: http://ctint-dab-qm-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  message:
    host : http://ctint-dab-message-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  manualqueue:
    host: http://ctint-dab-manualqueue-service.cdss-data-ctint.svc.cluster.local:5000
    basepath: /graphql
    active: true
  report:
    host: http://ctint-dab-report-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
ctint-state:
  auth:
    host: http://ctint-state-auth-service.cdss-data-ctint.svc.cluster.local:35000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /v1.0/state/ctint-state-auth
    active: true
  cdssmf:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint-state-cdssmf
    active: true
  session-manager:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint-state-session-manager
    active: true
portals:
- ctint-mf-cpp
services:
  ctint-stt:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/stt
    active: true
    provider: 
    - astri1.5
    healthcheck: /healthcheck
  ctint-nlp:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/nlp
    active: true
    provider: 
    - astri
    healthcheck: /healthcheck
  ctint-auth:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/auth
    active: true
    provider: # pure_engage / genesys_cloud / ctint-dab-auth / ctint-state-auth
    - genesys-cloud
    - ctint-dab-auth
    - ctint-state-auth
    healthcheck: /healthcheck
  ctint-conv:
    host: http://ctint-conv-app-service.ctint-conv.svc.cluster.local:8060
    basepath: /ctint-conv
    active: true
    provider: 
    - pureengage
    - ctint-dab-conv
    healthcheck: /healthcheck
    criteriaSearch:
    - labelEn: Type
      labelCh: 媒體類型
      value: mediaType
      filterType: select
      active: true
      isMetaData: false
    - labelEn: Users
      labelCh: 用戶資訊
      value: users
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Start Time
      labelCh: 開始時間
      value: conversationStart
      filterType: date
      active: true
      isMetaData: false
    - labelEn: End Time
      labelCh: 結束時間
      value: conversationEnd
      filterType: date
      active: true
      isMetaData: false
    - labelEn: Conversation ID
      labelCh: 對話 ID
      value: conversationId
      filterType: input
      active: false
      isMetaData: false
    - labelEn: ANI
      labelCh: 來電號碼
      value: ani
      filterType: input
      active: false
      isMetaData: false            
    - labelEn: DNIS
      labelCh: 撥號號碼
      value: dnis
      filterType: input
      active: false
      isMetaData: false
    - labelEn: Duration
      labelCh: 持續時間
      value: conversationDuration
      filterType: compare
      active: true
      isMetaData: false
    - labelEn: Queues
      labelCh: 队列
      value: queues
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Media Source
      labelCh: 媒體來源
      value: recordingMediaType
      filterType: input
      active: false
      isMetaData: false
    - labelEn: Customer Remote
      labelCh: 客户远程
      value: customerRemote
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Wrapups
      labelCh: 总结代码
      value: wrapups
      filterType: input
      active: true
      isMetaData: false 
    - labelEn: Provider
      labelCh: 提供者
      value: provider
      filterType: input
      active: false
      isMetaData: false 
    - labelEn: Recording
      labelCh: 是否录音
      value: recording
      filterType: bool
      active: false
      isMetaData: false
    - labelEn: Direction
      labelCh: 方向
      value: direction
      filterType: select
      active: true
      isMetaData: false 
    - labelEn: FinalResult
      labelCh: 最终结果
      value: finalResult
      filterType: select
      active: true
      isMetaData: false
    - labelEn: accountId
      labelCh: 账号Id
      value: accountId
      filterType: input
      active: false
      isMetaData: true
    - labelEn: cif
      labelCh: cif
      value: cif
      filterType: input
      active: true
      isMetaData: true
    - labelEn: Attachment
      labelCh: 附件
      value: attachment
      filterType: bool
      active: false
      isMetaData: false
    - labelEn: Case Id
      labelCh: Case Id
      value: caseId
      filterType: input
      active: false
      isMetaData: false
    - labelEn: Last Updated Agent
      labelCh: 最後更新代理
      value: lastUpdatedAgent
      filterType: input
      active: false
      isMetaData: false
  ctint-config:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/config
    active: true
    provider: 
    - pureengage
    - ctint-dab-config
    healthcheck: /healthcheck
  ctint-session:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session
    active: true
    provider: 
    - ctint-state-cdssmf
    healthcheck: /healthcheck
  ctint-session-manager:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session-manager
    active: true
    healthcheck: /healthcheck
  ctint-cdss-ws:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/cdss-ws
    active: true
    healthcheck: /healthcheck
  ctint-ccsp-ws:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/ccsp-ws
    active: true
    healthcheck: /healthcheck
  ctint-call-control:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/call-control
    active: true
    healthcheck: /healthcheck
  ctint-user:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/user
    active: true
    healthcheck: /healthcheck
    emailFile:
      storageType: azure_blob
  ctint-datasync-hub:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/datasync-hub
    active: true
    healthcheck: /healthcheck
  ctint-job-worker:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/job-worker
    active: true
    healthcheck: /healthcheck
  ctint-job-worker-auto-qm:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/job-worker-auto-qm
    active: true
    healthcheck: /healthcheck
  ctint-job-engine:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/job-engine
    active: true
    healthcheck: /healthcheck
  ctint-qm:
    host: https://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/qm
    active: true
    provider:
    healthcheck: /healthcheck
  ctint-audit-log:
    host: https://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/audit-log
    active: true
    provider:
    healthcheck: /healthcheck
  ctint-ocr:
    host: https://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/ocr
    active: true
    provider:
    healthcheck: /healthcheck
  ctint-manual-queue:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/cdss-manual-queue
    active: true
    healthcheck: /healthcheck
    email:
      permissionCheckList: true
      autoAssociationRef: true
      defaultSkillId: a62941c7-6cb5-4b95-89e6-72f2e95c25a8
      defaultSkillName: default_email_skill
      skillPrefix: email_
      reassignAgentQueueId: 7938dc78-7a19-4cc9-b068-c462d33888ea
    queueIds:
    - cf14c433-171f-4396-ae16-1522156d3583
    - dfeb95f4-9cb8-45c9-8114-7ca07f6924db
    - d950788f-19c2-4f84-8262-cdd59e60bee1
    voiceMailIds:
    - cf14c433-171f-4396-ae16-1522156d3583
    - dfeb95f4-9cb8-45c9-8114-7ca07f6924db
    callBackIds:
    - d950788f-19c2-4f84-8262-cdd59e60bee1
    messageIds:
    - c278d5d1-e527-450f-9e3a-24fb93f4dff8
    - ee7e2229-20bf-4bde-b732-67266c55e46f
    emailFile:
      storageType: azure_blob
      notProxyUrl: true
  ctint-report:
    host: https://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/report
    active: true
    provider:
    healthcheck: /healthcheck
  ctint-job-worker-report:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/job-worker-report
    active: true
    healthcheck: /healthcheck
  ctint-message:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/cdss-message
    active: true
    healthcheck: /healthcheck
    digital:
      proxy:
        url: https://digital-uat.cdss.ai/ctint/digital-proxy/api/v1/webhook/cdss
        token : Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpbnRlcm5hbFRva2VuIjoiY2Rzc19vdXRib3VuZCIsInRlbmFudCI6ImN0aW50In0.BB9n-QKKPlENqgXnGELSyPGOl8EtT_1w27kGHLWRBow
    genesys-cloud:
      outbound: 
        mode: direct
        dataActionId: custom_-_f0d70fb8-c0de-4bab-9e6f-1a8b25b073cc
        url: https://digital-uat.cdss.ai/ctint/digital-proxy/api/v1/webhook/genesysCloud/cdss/outbound
        token : Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpbnRlcm5hbFRva2VuIjoiY2Rzc19vdXRib3VuZCIsInRlbmFudCI6ImN0aW50In0.BB9n-QKKPlENqgXnGELSyPGOl8EtT_1w27kGHLWRBow
      externalApi: 
        mode: direct
        dataActionId: custom_-_75f51b62-4017-47d1-a2cf-4779d7f71638
        url: https://digital-uat.cdss.ai/ctint/digital-connector/api/v1/external/genesysCloud/dataAction
        token : Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpbnRlcm5hbFRva2VuIjoiY2Rzc19vdXRib3VuZCIsInRlbmFudCI6ImN0aW50In0.BB9n-QKKPlENqgXnGELSyPGOl8EtT_1w27kGHLWRBow
  ctint-campaign:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/campaign
    active: true
    healthcheck: /healthcheck

microfrontends:
  gc-client-id: 893e4921-45e9-49d8-9e45-3bf902007a50
  gc-auth-url: https://login.mypurecloud.com.au/oauth/authorize
  gc-webrtc-iframe-url: https://apps.mypurecloud.com.au/crm/embeddableFramework.html?enableFrameworkClientId=true
  errorCode:
    - 401
    - 403
  tenant: ctint
  homePageConfig:
    path: /
    permission: ctint-mf-super-dashboard.user.visit
    component: SuperDashboard
  headerEntries:
    - name: interactions
      icon: Rows4
      path: /interaction
      permission: ctint-mf-interaction.application.visit
    - name: users
      icon: Users 
      path: /admin/user
      permission: ctint-mf-admin.user.visit
    - name: audit
      icon: ClipboardList
      path: /admin/audit
      permission: ctint-mf-admin.audit.visit
    - name: qmadmin
      icon: FileSliders
      path: /admin/qm
      permission: ctint-mf-admin.qm.visit
    - name: superdashboard
      icon: SquareActivity
      path: /admin/super-dashboard
      permission: ctint-mf-admin.super-dashboard.visit
    - name: DNC&Blacklist
      icon: ListPlus
      path: /admin/dnc
      permission: ctint-mf-admin.dnc.visit
    - name: callpatch
      icon: Headset
      path: /call-patch
      permission: ctint-mf-agent.listing.visit
    - name: report
      icon: ScrollText
      path: /report
      permission: ctint-mf-report.application.visit
    - name: campaign
      icon: MessageSquarePlus
      path: /campaign
      permission: ctint-mf-campaign.application.visit
    - name: email-admin
      icon: Mail
      path: /admin/email
      permission: ctint-mf-admin.email.visit
    - name: auto-reply
      icon: MessageSquareReply
      path: /admin/auto-reply
      permission: ctint-mf-admin.auto-reply.visit
      component: AutoReplyAdmin
    - name: content-creation
      icon: FileCog
      path: /canned
      permission: ctint-mf-admin.content-creation.visit
  ctint-mf-cdss:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-cdss
    dnc-tab-names:
    - labelEn: Contact No.
      labelCh: 聯絡號碼
      value: contactNo
      filterType: input #select, date, compare, bool
      readOnly: false
      active: true
      require: true
      sort: true
      edit: false
    - labelEn: Customer Name
      labelCh: 客戶姓名
      value: customerName
      filterType: input #select, date, compare, bool
      readOnly: false
      active: true
      require: true
      sort: true
      edit: true
    - labelEn: Channel Type
      labelCh: 通話類型
      value: channelType
      filterType: input #select, date, compare, bool
      readOnly: false
      active: true
      require: true
      sort: true
      edit: true
    - labelEn: Reason
      labelCh: 原因
      value: reason
      filterType: input #select, date, compare, bool
      readOnly: false
      active: true
      require: true
      edit: true
    - labelEn: Create At
      labelCh: 創建時間
      value: createAt
      filterType: input #select, date, compare, bool
      readOnly: false
      active: true
      require: true
      sort: true
      edit: false
    - labelEn: Update At
      labelCh: 更新時間
      value: updateAt
      filterType: input #select, date, compare, bool
      readOnly: false
      active: true
      require: true
      sort: true
      edit: false
    manual-queue:
    - queueName: Voice Mail
      type: voicemail
    - queueName: Callback
      type: callback
    - queueName: Email
      type: email
    - queueName: Message
      type: message
    user-tab-names:
    - labelEn: Name
      labelCh: 姓名
      value: name
      filterType: input #select, date, compare, bool
      readOnly: false
      active: true
      require: true
    - labelEn: Username
      labelCh: 用戶賬號
      value: email
      filterType: input
      readOnly: false
      active: true
      require: true
    - labelEn: State
      labelCh: 狀態
      value: state
      filterType: select
      readOnly: false
      active: true
      require: false
    - labelEn: Login Type
      labelCh: 登录方式
      value: authtype
      filterType: select
      readOnly: true
      active: true
      require: false
    - labelEn: Description
      labelCh: 描述
      value: description
      filterType: input
      readOnly: false
      active: true
      require: false
    - labelEn: User Groups
      labelCh: 用戶組
      value: groupNames
      filterType: multipleSelect
      readOnly: false
      active: true
      require: false
    - labelEn: Roles
      labelCh: 角色
      value: roleNames
      filterType: multipleSelect
      readOnly: false
      active: true
      require: false
    - labelEn: Division
      labelCh: 部門
      value: divisionName
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Platform
      labelCh: 平台
      value: platform
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Tenant
      labelCh: 租戶
      value: tenant
      readOnly: true
      filterType: input
      active: true
      require: false
    - labelEn: Created By
      labelCh: 創建用戶
      value: createBy
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Create At
      labelCh: 創建時間
      value: createTime
      filterType: dateRange
      readOnly: true
      active: true
      require: false
    - labelEn: Update By
      labelCh: 更新用戶
      value: updateBy
      filterType: input
      readOnly: true
      active: true
      require: false
    - labelEn: Update At
      labelCh: 更新時間
      value: updateTime
      filterType: dateRange
      readOnly: true
      active: true
      require: false
    audit-tab-names: 
    - labelEn: Event Type
      labelCh: 事件類型
      value: eventType
      filterType: input
      readOnly: false
      active: true
      sort: false
    - labelEn: Event Time
      labelCh: 事件時間
      value: eventTimestamp
      filterType: dateRange
      readOnly: false
      active: true
      sort: true
    - labelEn: IP Address
      labelCh: IP地址
      value: ipAddress
      filterType: input
      readOnly: false
      active: true
      sort: false
    - labelEn: User Agent
      labelCh: 用戶代理
      value: userAgent
      filterType: input
      readOnly: true
      active: true
      sort: false
    - labelEn: Browser
      labelCh: 瀏覽器
      value: browser
      filterType: input
      readOnly: false
      active: true
      sort: false
    - labelEn: Operating System
      labelCh: 操作系統
      value: operatingSystem
      readOnly: true
      filterType: input
      active: true
      sort: false
    - labelEn: Device
      labelCh: 設備
      value: device
      filterType: input
      readOnly: true
      active: true
      sort: false
    - labelEn: Failure Reason
      labelCh: 失敗原因
      value: failureReason
      filterType: input
      readOnly: true
      active: true
      sort: false
    - labelEn: Additional Info
      labelCh: 附加信息
      value: additionalInfo
      filterType: input
      readOnly: true
      active: true
      sort: false
    message-outbound-enable-queue:
      - queueName: Simone testing
        queueId: c278d5d1-e527-450f-9e3a-24fb93f4dff8
        type: message
    autoAnswerList:
      - messages
      - callbacks
      - voicemails
      - emails
      - calls
    useExistingConversation: true
    functionConfig:
      enableOutboundCall: true
      enableSelectStation: true
      enableSelectStatus: true
      enableOnQueueSwitch: true
      enableGreeting: true
      wrapupList: true
      miniWallBoard: true
      queueList: true
      userList: true
      userQueueList: true
      conversationHistoryList: true
      conversationActiveList: true
  ctint-mf-interaction:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-interaction
  ctint-mf-manual-queue:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-manual-queue
    permission:
      email-assign-to: ctint-mf-manual-queue.email.assign
      callback-assign-to: ctint-mf-manual-queue.callback.assign
      voicemail-assign-to: ctint-mf-manual-queue.voicemail.assign
    manual-tab-names:
      - labelEn: ConversationID
        labelCh: 對話 ID
        value: conversationId
        filterType: input
        active: false
        isMetaData: false
      - labelEn: Type
        labelCh: 媒體類型
        value: mediaType
        filterType: select
        active: true
        require: false
        isMetaData: false
      - labelEn: State
        labelCh: 狀態
        value: state
        active: true
        require: false
        isMetaData: false
      - labelEn: From
        labelCh: 來自
        value: from
        filterType: input
        active: true
        isMetaData: false
      - labelEn: CurrentAssignee
        labelCh: 目前處理者
        value: currentAgent
        active: true
      - labelEn: AssignmentRecords
        labelCh: 委派記錄
        value: assign
        filterType: select
        active: true
        isMetaData: false
      - labelEn: CallBackNumber
        labelCh: 回撥號碼
        value: callbackNumber
        filterType: input
        active: true
        isMetaData: false
      - labelEn: QueueName
        labelCh: 佇列
        value: queueName
        filterType: select
        active: true
        isMetaData: true
      - labelEn: CreatedAt
        labelCh: 開始時間
        value: createdAt
        filterType: dateRange
        active: true
        isMetaData: false
        checked: false
        require: true
      - labelEn: ConversationDuration
        labelCh: 對話持續期間
        value: conversationDuration
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Ended
        labelCh: 已結束
        value: ended
        filterType: select
        active: true
        isMetaData: false
    email-tab-names:
      - labelEn: ConversationID
        labelCh: 對話 ID
        value: conversationId
        filterType: input
        active: false
        isMetaData: false
      - labelEn: Type
        labelCh: 媒體類型
        value: mediaType
        filterType: select
        active: true
        require: false
        isMetaData: false
      - labelEn: CaseID
        labelCh: 案例編號
        value: caseId
        filterType: input
        active: true
        require: false
        isMetaData: false
      - labelEn: CaseStatus
        labelCh: 案例狀態
        value: caseStatus
        filterType: select
        active: true
        require: false
        isMetaData: false
      - labelEn: Subject
        labelCh: 主體
        value: subject
        filterType: input
        active: true
        require: false
        isMetaData: false
      - labelEn: State
        labelCh: 狀態
        value: emailState
        filterType: select
        active: true
        require: false
        isMetaData: false
      - labelEn: RefID
        labelCh: 參考編號
        value: referenceId
        filterType: input
        active: true
        require: false
        isMetaData: false
      - labelEn: RefStatus
        labelCh: 參考狀態
        value: referenceStatus
        filterType: select
        active: true
        require: false
        isMetaData: false
      - labelEn: From
        labelCh: 來自
        value: from
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Assign
        labelCh: 委派
        value: assign
        filterType: select
        active: true
        isMetaData: false
      - labelEn: QueueName
        labelCh: 佇列
        value: queueName
        filterType: select
        active: true
        isMetaData: true
      - labelEn: Content
        labelCh: 内容
        value: emailBody
        filterType: input
        active: true
      - labelEn: CreatedAt
        labelCh: 開始時間
        value: createdTime
        filterType: dateRange
        active: true
        isMetaData: true
        checked: false
        require: false
      - labelEn: JunkMail
        labelCh: 垃圾郵件
        value: isJunkmail
        filterType: select
        active: true
        isMetaData: true
        checked: false
        require: false
      - labelEn: Direction
        labelCh: 方向
        value: emailDirection
        filterType: select
        active: true
        isMetaData: true
        checked: false
        require: false
    message-tab-names:
      - labelEn: Type
        labelCh: 媒體類型
        value: mediaType
        filterType: select
        active: true
        require: false
        isMetaData: false
      - labelEn: Conversation ID
        labelCh: 對話 ID
        value: conversationId
        filterType: input
        active: true
        isMetaData: true
      - labelEn: State
        labelCh: 狀態
        value: state
        active: true
        require: false
        isMetaData: false
      - labelEn: Remote
        labelCh: 來自
        value: from
        filterType: input
        active: true
        isMetaData: false
      - labelEn: CurrentAgent
        labelCh: 当前Agent
        value: currentAgent
        active: true
      - labelEn: Assign
        labelCh: 委派
        value: assign
        filterType: select
        active: true
        isMetaData: false
      - labelEn: CurrentQueue
        labelCh: 当前佇列
        value: currentQueue
        filterType: select
        active: true
        isMetaData: true
      - labelEn: QueueName
        labelCh: 佇列
        value: queueName
        filterType: select
        active: true
        isMetaData: true
      - labelEn: Created At
        labelCh: 開始時間
        value: createdAt
        filterType: dateRange
        active: true
        isMetaData: false
        checked: false
        require: true
      - labelEn: ConversationDuration
        labelCh: 對話持續期間
        value: conversationDuration
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Direction
        labelCh: 方向
        value: direction
        filterType: select
        active: true
        isMetaData: true
        checked: false
        require: false
      - labelEn: Ended
        labelCh: 已結束
        value: ended
        filterType: select
        active: true
        isMetaData: false
    options:
      caseStatus:
        - id: Connecting
          value: Connecting
          label: Connecting
        - id: Connected
          value: Connected
          label: Connected
        - id: Waiting
          value: Waiting
          label: Waiting
        - id: Processing
          value: Processing
          label: Processing
        - id: Completed
          value: Completed
          label: Completed
      referenceStatus:
        - id: TEST
          value: TEST
          label: TEST
        - id: PENDING
          value: PENDING
          label: PENDING
        - id: COMPLETED
          value: COMPLETED
          label: COMPLETED
        - id: FAILED
          value: FAILED
          label: FAILED
    hiddenMiniWallboard: true
    enableRightWindow: true
    enableCallInfo: true
    enableSAA: true
    emailReference: true
    emailSecureSuffix: "[Secure]"
    emailSecureSuffixButtonName: "Secure Email"
    emailSkillFlow: true
  ctint-mf-super-dashboard:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-super-dashboard
  ctint-mf-message:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-message
    useSAAv2: true
    GCMessageSupportedId: cbfcb843-0e1d-41d7-ac01-4ae45dbd8db6
    enableRightWindow: true
    enableCallInfo: true
    enableSAA: true
  ctint-mf-msg:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-message
  ctint-mf-call:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-call
  ctint-mf-report:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-report
  ctint-mf-campaign:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-campaign
    broadcast-tab-names:
      - labelEn: ID
        labelCh: ID
        value: id
        active: false
        isMetaData: false
      - labelEn: Broadcast Name
        labelCh: 廣播名稱
        value: name
        filterType: input
        active: true
        isMetaData: false
      - labelEn: From
        labelCh: 來自
        value: from
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Message Template
        labelCh: 訊息模板
        value: templateName
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Date
        labelCh: 日期
        value: sendDate
        filterType: dateRange
        active: true
        isMetaData: false
      - labelEn: Resend Schedule
        labelCh: 重發計劃
        value: resendDate
        filterType: dateRange
        active: true
        isMetaData: false
      - labelEn: Status
        labelCh: 狀態
        value: status
        filterType: select
        active: true
        isMetaData: false
      - labelEn: Create User
        labelCh: 創建人
        value: createUser
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Total
        labelCh: 總數
        value: total
        active: true
      - labelEn: Sent
        labelCh: 已發送
        value: sentCount
        active: true
      - labelEn: Success
        labelCh: 成功
        value: successCount
        active: true
      - labelEn: Read
        labelCh: 已讀
        value: readCount
        active: true
      - labelEn: Replied
        labelCh: 已回覆
        value: repliedCount
        active: true
      - labelEn: Error
        labelCh: 錯誤
        value: errorCount
        active: true
  ctint-mf-content-creation:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-content-creation
    spaceColumn:
      - labelEn: 'Space Name'
        labelCh: '空間名稱'
        value: 'name'
        filterType: 'input'
        active: true
        isMetaData: false
      - labelEn: 'Created By'
        labelCh: '創建者'
        value: 'createBy'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Last Updated By'
        labelCh: '最後更新用戶'
        value: 'lastUpdateBy'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Channel Type'
        labelCh: '渠道類型'
        value: 'channelType'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Create At'
        labelCh: '創建時間'
        value: 'createDate'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Last Updated'
        labelCh: '最後更新時間'
        value: 'lastUpdate'
        filterType: none
        active: true
        isMetaData: false
    categoryColumn:
      - labelEn: 'Category Name'
        labelCh: '類別名稱'
        value: 'name'
        filterType: 'input'
        active: true
        isMetaData: false
      - labelEn: 'Created By'
        labelCh: '創建者'
        value: 'createBy'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Last Updated By'
        labelCh: '最後更新用戶'
        value: 'lastUpdateBy'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Channel Type'
        labelCh: '渠道類型'
        value: 'channelType'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Create At'
        labelCh: '創建時間'
        value: 'createDate'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Last Updated'
        labelCh: '最後更新時間'
        value: 'lastUpdate'
        filterType: none
        active: true
        isMetaData: false
    templateColumn:
      - labelEn: 'Template Name'
        labelCh: '模板名稱'
        value: 'name'
        filterType: 'input'
        active: true
        isMetaData: false
      - labelEn: 'Status'
        labelCh: '狀態'
        value: 'status'
        filterType: 'input'
        active: true
        isMetaData: false
      - labelEn: 'Created By'
        labelCh: '創建者'
        value: 'createBy'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Last Updated By'
        labelCh: '最後更新用戶'
        value: 'lastUpdateBy'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Channel Type'
        labelCh: '渠道類型'
        value: 'channelType'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Create At'
        labelCh: '創建時間'
        value: 'createDate'
        filterType: none
        active: true
        isMetaData: false
      - labelEn: 'Last Updated'
        labelCh: '最後更新時間'
        value: 'lastUpdate'
        filterType: none
        active: true
        isMetaData: false
languages:
  supportedLanguages:
  - en
  - zh-HK
  defaultLanguage: en
settings:
  defaultStorage: aws
aws:
  default:
    bucket: ctint-cpp-recording-bucket
    region: ap-east-1
    accessKeyIdVariableName: AWS_ACCESS_KEY_ID
    secretAccessKeyVariableName: AWS_SECRET_ACCESS_KEY
  stt:
    bucket: ctint-cpp-recording-bucket
    region: ap-east-1
    accessKeyIdVariableName: AWS_ACCESS_KEY_ID
    secretAccessKeyVariableName: AWS_SECRET_ACCESS_KEY
  ocr:
    bucket: ctint-cpp-recording-bucket
    region: ap-east-1
    accessKeyIdVariableName: AWS_ACCESS_KEY_ID
    secretAccessKeyVariableName: AWS_SECRET_ACCESS_KEY
  email:
    bucket: digital-email-s3
    region: ap-east-1
    accessKeyIdVariableName: AWS_ACCESS_KEY_ID
    secretAccessKeyVariableName: AWS_SECRET_ACCESS_KEY
permissionRelease: true
azure:
  default:
    storageAccountNameVariableName: AZURE_STORAGE_ACCOUNT_NAME
    storageAccountKeyVariableName: AZURE_STORAGE_ACCOUNT_KEY
    StorageAccountTimeout: 1
    storageAccountContainerName: recording
  email:
    storageAccountNameVariableName: AZURE_STORAGE_ACCOUNT_NAME
    storageAccountKeyVariableName: AZURE_STORAGE_ACCOUNT_KEY
    StorageAccountTimeout: 1
    storageAccountContainerName: ctint-email-attachment
redis:
#  url: digitaltestssl.redis.cache.windows.net
#  port: 6380
#  password: 2UCN3gFIPg1MDmshqhk1tRH9isQOZLbMHAzCaOwo8IU=
  url: digital.redis.cache.windows.net
  port: 6379
  password: aLOiX6vK2idUSzVdYxNvcWAYT5sMrX6bmAzCaDOvF5I=
  ctint-manual-queue:
    index: 0
  ctint-message:
    index: 0